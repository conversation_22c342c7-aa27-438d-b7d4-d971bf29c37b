//
//  ScratchCardConfigTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/7/31.
//

import XCTest
import CoreData
@testable import ztt2

/**
 * 刮刮卡配置功能测试类
 * 测试刮刮卡配置的保存、加载、更新、验证等功能
 */
final class ScratchCardConfigTests: XCTestCase {
    
    var persistenceController: PersistenceController!
    var testContext: NSManagedObjectContext!
    var dataManager: DataManager!
    var testMember: Member!
    
    override func setUpWithError() throws {
        // 使用内存存储进行测试
        persistenceController = PersistenceController(inMemory: true)
        testContext = persistenceController.container.viewContext
        
        // 创建测试用的DataManager实例
        dataManager = DataManager.shared
        dataManager.persistenceController = persistenceController
        
        // 创建测试成员
        let user = User(context: testContext)
        user.id = UUID()
        user.nickname = "测试用户"
        user.createdAt = Date()
        
        testMember = Member(context: testContext)
        testMember.id = UUID()
        testMember.name = "测试成员"
        testMember.role = "son"
        testMember.currentPoints = 100
        testMember.totalPoints = 100
        testMember.createdAt = Date()
        testMember.user = user
        
        try testContext.save()
    }
    
    override func tearDownWithError() throws {
        persistenceController = nil
        testContext = nil
        dataManager = nil
        testMember = nil
    }
    
    // MARK: - 刮刮卡配置保存测试
    
    func testSaveScratchCardConfig() throws {
        // 准备测试数据
        let cardCount = 5
        let costPerPlay = 20
        let cardPrizes = ["小贴纸", "铅笔", "橡皮", "尺子", "小玩具"]
        
        // 保存刮刮卡配置
        let savedConfig = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: cardCount,
            costPerPlay: costPerPlay,
            cardPrizes: cardPrizes
        )
        
        // 验证保存结果
        XCTAssertNotNil(savedConfig, "刮刮卡配置应该保存成功")
        XCTAssertEqual(savedConfig?.itemCount, Int32(cardCount), "刮刮卡数量应该正确")
        XCTAssertEqual(savedConfig?.costPerPlay, Int32(costPerPlay), "消耗积分应该正确")
        XCTAssertEqual(savedConfig?.toolType, "scratchcard", "工具类型应该是刮刮卡")
        XCTAssertEqual(savedConfig?.member, testMember, "应该关联到正确的成员")
        
        // 验证奖品项目
        let items = savedConfig?.allItems ?? []
        XCTAssertEqual(items.count, cardCount, "奖品项目数量应该正确")
        
        for (index, expectedPrize) in cardPrizes.enumerated() {
            let item = items.first { $0.itemIndex == index }
            XCTAssertNotNil(item, "索引 \(index) 的奖品项目应该存在")
            XCTAssertEqual(item?.prizeName, expectedPrize, "索引 \(index) 的奖品名称应该正确")
        }
    }
    
    // MARK: - 刮刮卡配置加载测试
    
    func testGetScratchCardConfig() throws {
        // 先保存一个配置
        let cardCount = 3
        let costPerPlay = 15
        let cardPrizes = ["奖品1", "奖品2", "奖品3"]
        
        let _ = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: cardCount,
            costPerPlay: costPerPlay,
            cardPrizes: cardPrizes
        )
        
        // 加载配置
        let loadedConfig = dataManager.getScratchCardConfig(for: testMember)
        
        // 验证加载结果
        XCTAssertNotNil(loadedConfig, "应该能够加载刮刮卡配置")
        XCTAssertEqual(loadedConfig?.itemCount, Int32(cardCount), "刮刮卡数量应该正确")
        XCTAssertEqual(loadedConfig?.costPerPlay, Int32(costPerPlay), "消耗积分应该正确")
        XCTAssertEqual(loadedConfig?.toolType, "scratchcard", "工具类型应该是刮刮卡")
    }
    
    // MARK: - 刮刮卡配置更新测试
    
    func testUpdateScratchCardConfig() throws {
        // 先保存一个配置
        let initialCardCount = 3
        let initialCostPerPlay = 10
        let initialCardPrizes = ["奖品1", "奖品2", "奖品3"]
        
        let _ = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: initialCardCount,
            costPerPlay: initialCostPerPlay,
            cardPrizes: initialCardPrizes
        )
        
        // 更新配置
        let updatedCardCount = 4
        let updatedCostPerPlay = 25
        let updatedCardPrizes = ["新奖品1", "新奖品2", "新奖品3", "新奖品4"]
        
        let updatedConfig = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: updatedCardCount,
            costPerPlay: updatedCostPerPlay,
            cardPrizes: updatedCardPrizes
        )
        
        // 验证更新结果
        XCTAssertNotNil(updatedConfig, "配置更新应该成功")
        XCTAssertEqual(updatedConfig?.itemCount, Int32(updatedCardCount), "更新后的刮刮卡数量应该正确")
        XCTAssertEqual(updatedConfig?.costPerPlay, Int32(updatedCostPerPlay), "更新后的消耗积分应该正确")
        
        // 验证只有一个配置存在（更新而不是创建新的）
        let allConfigs = testMember.allLotteryConfigs.filter { $0.lotteryToolType == .scratchcard }
        XCTAssertEqual(allConfigs.count, 1, "应该只有一个刮刮卡配置")
        
        // 验证奖品项目已更新
        let items = updatedConfig?.allItems ?? []
        XCTAssertEqual(items.count, updatedCardCount, "更新后的奖品项目数量应该正确")
        
        for (index, expectedPrize) in updatedCardPrizes.enumerated() {
            let item = items.first { $0.itemIndex == index }
            XCTAssertNotNil(item, "索引 \(index) 的奖品项目应该存在")
            XCTAssertEqual(item?.prizeName, expectedPrize, "索引 \(index) 的奖品名称应该正确")
        }
    }
    
    // MARK: - 刮刮卡配置删除测试
    
    func testDeleteScratchCardConfig() throws {
        // 先保存一个配置
        let cardCount = 5
        let costPerPlay = 20
        let cardPrizes = ["奖品1", "奖品2", "奖品3", "奖品4", "奖品5"]
        
        let _ = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: cardCount,
            costPerPlay: costPerPlay,
            cardPrizes: cardPrizes
        )
        
        // 验证配置存在
        let configBeforeDelete = dataManager.getScratchCardConfig(for: testMember)
        XCTAssertNotNil(configBeforeDelete, "删除前配置应该存在")
        
        // 删除配置
        let deleteResult = dataManager.deleteScratchCardConfig(for: testMember)
        XCTAssertTrue(deleteResult, "删除操作应该成功")
        
        // 验证配置已删除
        let configAfterDelete = dataManager.getScratchCardConfig(for: testMember)
        XCTAssertNil(configAfterDelete, "删除后配置应该不存在")
        
        // 测试删除不存在的配置
        let deleteNonExistentResult = dataManager.deleteScratchCardConfig(for: testMember)
        XCTAssertFalse(deleteNonExistentResult, "删除不存在的配置应该返回false")
    }
    
    // MARK: - 数据验证测试
    
    func testScratchCardConfigValidation() throws {
        // 测试无效的刮刮卡数量（超出范围）
        let invalidConfig1 = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: 0, // 小于最小值
            costPerPlay: 10,
            cardPrizes: []
        )
        XCTAssertNil(invalidConfig1, "刮刮卡数量为0的配置应该保存失败")
        
        let invalidConfig2 = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: 25, // 大于最大值
            costPerPlay: 10,
            cardPrizes: Array(repeating: "奖品", count: 25)
        )
        XCTAssertNil(invalidConfig2, "刮刮卡数量为25的配置应该保存失败")
        
        // 测试无效的积分值
        let invalidConfig3 = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: 5,
            costPerPlay: -10, // 负数
            cardPrizes: Array(repeating: "奖品", count: 5)
        )
        XCTAssertNil(invalidConfig3, "负积分的配置应该保存失败")
        
        // 测试奖品数量不匹配
        let invalidConfig4 = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: 5,
            costPerPlay: 10,
            cardPrizes: ["奖品1", "奖品2"] // 数量不匹配
        )
        XCTAssertNil(invalidConfig4, "奖品数量不匹配的配置应该保存失败")
        
        // 测试空奖品名称
        let invalidConfig5 = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: 3,
            costPerPlay: 10,
            cardPrizes: ["奖品1", "", "奖品3"] // 包含空名称
        )
        XCTAssertNil(invalidConfig5, "包含空奖品名称的配置应该保存失败")
        
        // 测试过长的奖品名称
        let longPrizeName = String(repeating: "很长的奖品名称", count: 10) // 超过20个字符
        let invalidConfig6 = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: 2,
            costPerPlay: 10,
            cardPrizes: ["正常奖品", longPrizeName]
        )
        XCTAssertNil(invalidConfig6, "包含过长奖品名称的配置应该保存失败")
    }
    
    // MARK: - 边界值测试
    
    func testScratchCardConfigBoundaryValues() throws {
        // 测试最小有效配置
        let minConfig = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: LotteryConfig.ToolType.scratchcard.minItemCount,
            costPerPlay: 0,
            cardPrizes: Array(repeating: "奖品", count: LotteryConfig.ToolType.scratchcard.minItemCount)
        )
        XCTAssertNotNil(minConfig, "最小有效配置应该保存成功")
        
        // 测试最大有效配置
        let maxConfig = dataManager.saveScratchCardConfig(
            for: testMember,
            cardCount: LotteryConfig.ToolType.scratchcard.maxItemCount,
            costPerPlay: 999,
            cardPrizes: Array(repeating: "奖品", count: LotteryConfig.ToolType.scratchcard.maxItemCount)
        )
        XCTAssertNotNil(maxConfig, "最大有效配置应该保存成功")
    }
    
    // MARK: - 性能测试
    
    func testScratchCardConfigPerformance() throws {
        // 测试保存配置的性能
        measure {
            for i in 0..<10 {
                let _ = dataManager.saveScratchCardConfig(
                    for: testMember,
                    cardCount: 5,
                    costPerPlay: 10 + i,
                    cardPrizes: ["奖品1", "奖品2", "奖品3", "奖品4", "奖品5"]
                )
            }
        }
    }
}
