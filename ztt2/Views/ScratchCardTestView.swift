//
//  ScratchCardTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡功能测试视图
 * 用于验证刮刮卡功能的完整性
 */
struct ScratchCardTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var showScratchCard = false
    @State private var testMember: Member?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("刮刮卡功能测试")
                    .font(.title)
                    .fontWeight(.bold)
                
                if let member = testMember {
                    VStack(spacing: 10) {
                        Text("测试成员: \(member.name ?? "未知")")
                        Text("当前积分: \(member.currentPoints)")
                        
                        But<PERSON>("打开刮刮卡") {
                            showScratchCard = true
                        }
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                } else {
                    VStack(spacing: 10) {
                        Text("正在创建测试成员...")
                        
                        But<PERSON>("创建测试成员") {
                            createTestMember()
                        }
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("刮刮卡测试")
            .onAppear {
                setupTestData()
            }
        }
        .fullScreenCover(isPresented: $showScratchCard) {
            if let member = testMember {
                NavigationView {
                    ScratchCardView(
                        member: member,
                        onDismiss: {
                            showScratchCard = false
                        }
                    )
                }
                .environmentObject(dataManager)
            }
        }
    }
    
    private func setupTestData() {
        // 查找或创建测试成员
        let context = dataManager.persistenceController.container.viewContext
        let request = Member.fetchRequest()
        request.predicate = NSPredicate(format: "name == %@", "测试成员")

        do {
            let members = try context.fetch(request)
            if let existingMember = members.first {
                testMember = existingMember
                // 确保有足够积分
                if existingMember.currentPoints < 100 {
                    existingMember.currentPoints = 100
                    dataManager.save()
                }
            } else {
                createTestMember()
            }
        } catch {
            print("获取测试成员失败: \(error)")
            createTestMember()
        }

        // 创建测试刮刮卡配置
        createTestScratchCardConfig()
    }
    
    private func createTestMember() {
        let context = dataManager.persistenceController.container.viewContext
        let member = Member(context: context)
        member.id = UUID()
        member.name = "测试成员"
        member.role = "儿子"
        // 设置出生日期为10年前，这样age计算属性会返回10
        member.birthDate = Calendar.current.date(byAdding: .year, value: -10, to: Date())
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()

        dataManager.save()
        testMember = member
    }
    
    private func createTestScratchCardConfig() {
        guard let member = testMember else { return }
        
        // 检查是否已有配置
        if member.getLotteryConfig(for: .scratchcard) != nil {
            return
        }
        
        // 创建刮刮卡配置
        let prizes = ["小贴纸", "铅笔", "橡皮", "尺子", "谢谢参与", "小玩具"]
        let _ = dataManager.saveScratchCardConfig(
            for: member,
            cardCount: 6,
            costPerPlay: 10,
            cardPrizes: prizes
        )
    }
}

#Preview {
    ScratchCardTestView()
        .environmentObject(DataManager.shared)
}
