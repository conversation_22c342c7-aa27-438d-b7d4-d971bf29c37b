//
//  ScratchCardStylePreview.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡样式预览视图
 * 用于展示修改后的刮刮卡界面效果
 */
struct ScratchCardStylePreview: View {
    
    @StateObject private var dataManager = DataManager.shared
    @State private var testMember: Member?
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    // 新的白色系背景
                    LinearGradient(
                        colors: [
                            Color(hex: "#ffeef0").opacity(0.3),
                            Color(hex: "#fff8e1").opacity(0.2),
                            Color.white
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .ignoresSafeArea()
                    
                    VStack(spacing: 0) {
                        // 顶部统计信息（新样式）
                        scratchCardStatsView
                            .padding(.top, 20)
                            .padding(.bottom, 16)
                        
                        // 刮刮卡网格预览
                        scratchCardGridPreview(geometry: geometry)
                    }
                }
            }
            .navigationTitle("刮刮卡样式预览")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            setupTestMember()
        }
    }
    
    // MARK: - Stats View (新样式)
    
    private var scratchCardStatsView: some View {
        VStack(spacing: 12) {
            HStack {
                // 剩余卡片数
                HStack(spacing: 8) {
                    Image(systemName: "square.stack.3d.up")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#ff6b6b"))
                    
                    Text("剩余：12 张")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                
                Spacer()
                
                // 消耗积分
                HStack(spacing: 8) {
                    Image(systemName: "star.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#a9d051"))
                    
                    Text("每张：1 积分")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
            
            // 当前积分
            HStack(spacing: 8) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.blue)
                
                Text("当前积分：24")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Spacer()
                
                // 积分状态
                Text("积分充足")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color.green)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.green.opacity(0.1))
                    )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 20)
    }
    
    // MARK: - Grid Preview
    
    private func scratchCardGridPreview(geometry: GeometryProxy) -> some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVGrid(columns: gridColumns(for: geometry), spacing: 12) {
                ForEach(0..<9, id: \.self) { index in
                    sampleCardView(index: index)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            .padding(.bottom, 40)
        }
    }
    
    private func gridColumns(for geometry: GeometryProxy) -> [GridItem] {
        let availableWidth = geometry.size.width - 40
        let cardWidth = (availableWidth - 24) / 3 // 3列，间距12
        return Array(repeating: GridItem(.fixed(cardWidth), spacing: 12), count: 3)
    }
    
    private func sampleCardView(index: Int) -> some View {
        let isScratched = index == 2 // 第3张卡片显示为已刮开状态
        let cardWidth = (UIScreen.main.bounds.width - 64) / 3
        let cardHeight = cardWidth / 0.75
        
        return VStack(spacing: 8) {
            // 卡片主体
            ZStack {
                // 新的卡片背景样式
                cardBackground(isScratched: isScratched)
                
                // 卡片内容
                cardContent(isScratched: isScratched, cardWidth: cardWidth)
            }
            .frame(width: cardWidth, height: cardHeight)
            
            // 卡片信息
            VStack(spacing: 4) {
                Text("刮刮卡 \(index + 1)")
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                // 新的状态指示器样式
                statusIndicator(isScratched: isScratched, isClickable: index < 6)
            }
        }
    }
    
    private func cardBackground(isScratched: Bool) -> some View {
        ZStack {
            // 主背景渐变
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: isScratched ? [
                            Color(hex: "#e8f5e8"),
                            Color(hex: "#f0f9f0")
                        ] : [
                            Color(hex: "#ffeef0"),
                            Color(hex: "#fff8e1")
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // 光泽效果层
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.6),
                            Color.clear,
                            Color.clear,
                            Color.white.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // 边框
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: isScratched ? [
                            Color.green.opacity(0.4),
                            Color.green.opacity(0.2)
                        ] : [
                            Color(hex: "#ff6b6b").opacity(0.3),
                            Color(hex: "#a9d051").opacity(0.3)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        }
        .shadow(
            color: isScratched ? 
                Color.green.opacity(0.2) : Color.black.opacity(0.12),
            radius: isScratched ? 8 : 6,
            x: 0,
            y: isScratched ? 4 : 3
        )
    }
    
    private func cardContent(isScratched: Bool, cardWidth: CGFloat) -> some View {
        VStack(spacing: 8) {
            if isScratched {
                // 中奖内容
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .fill(
                                RadialGradient(
                                    colors: [
                                        Color.green.opacity(0.4),
                                        Color.yellow.opacity(0.3),
                                        Color.clear
                                    ],
                                    center: .center,
                                    startRadius: 8,
                                    endRadius: 30
                                )
                            )
                            .frame(width: cardWidth * 0.7, height: cardWidth * 0.7)
                        
                        Image(systemName: "gift.fill")
                            .font(.system(size: cardWidth * 0.3, weight: .bold))
                            .foregroundColor(.green)
                    }
                    
                    Text("已中奖")
                        .font(.system(size: 15, weight: .bold))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [Color.green, Color(hex: "#32d74b")],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                }
            } else {
                // 未刮开内容
                VStack(spacing: 10) {
                    ZStack {
                        Circle()
                            .fill(
                                RadialGradient(
                                    colors: [
                                        Color(hex: "#ff6b6b").opacity(0.3),
                                        Color(hex: "#a9d051").opacity(0.2),
                                        Color.clear
                                    ],
                                    center: .center,
                                    startRadius: 5,
                                    endRadius: 25
                                )
                            )
                            .frame(width: cardWidth * 0.6, height: cardWidth * 0.6)
                        
                        Image(systemName: "questionmark")
                            .font(.system(size: cardWidth * 0.3, weight: .bold))
                            .foregroundColor(Color(hex: "#ff6b6b"))
                    }
                    
                    Text("刮一刮")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [
                                    Color(hex: "#ff6b6b"),
                                    Color(hex: "#a9d051")
                                ],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                }
            }
        }
        .padding(12)
    }
    
    private func statusIndicator(isScratched: Bool, isClickable: Bool) -> some View {
        let color = isScratched ? Color.green : (isClickable ? Color(hex: "#a9d051") : Color.gray)
        let icon = isScratched ? "trophy.fill" : (isClickable ? "hand.tap.fill" : "clock.fill")
        let text = isScratched ? "已中奖" : (isClickable ? "可刮除" : "等待中")
        
        return HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 10, weight: .semibold))
                .foregroundColor(color)
            
            Text(text)
                .font(.system(size: 10, weight: .semibold))
                .foregroundColor(color)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 5)
        .background(
            Capsule()
                .fill(
                    LinearGradient(
                        colors: [
                            color.opacity(0.15),
                            color.opacity(0.08)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
        .overlay(
            Capsule()
                .stroke(
                    LinearGradient(
                        colors: [
                            color.opacity(0.6),
                            color.opacity(0.4)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1.5
                )
        )
        .shadow(
            color: color.opacity(0.3),
            radius: 3,
            x: 0,
            y: 1
        )
    }
    
    private func setupTestMember() {
        let member = Member(context: dataManager.persistenceController.container.viewContext)
        member.id = UUID()
        member.name = "测试成员"
        member.currentPoints = 24
        member.createdAt = Date()
        member.updatedAt = Date()
        testMember = member
    }
}

#Preview {
    ScratchCardStylePreview()
}
