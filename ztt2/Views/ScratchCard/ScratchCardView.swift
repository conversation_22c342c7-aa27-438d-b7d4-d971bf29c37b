//
//  ScratchCardView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡主视图
 * 集成所有组件并实现完整的刮刮卡界面
 */
struct ScratchCardView: View {
    
    // MARK: - Properties
    let member: Member
    let onDismiss: () -> Void
    
    // MARK: - Dependencies
    @EnvironmentObject private var dataManager: DataManager
    
    // MARK: - State Properties
    @StateObject private var viewModel: ScratchCardViewModel
    @State private var showBackAlert = false
    @State private var showScratchCardConfigPopup = false
    
    // MARK: - Initialization
    
    init(member: Member, onDismiss: @escaping () -> Void) {
        self.member = member
        self.onDismiss = onDismiss
        self._viewModel = StateObject(wrappedValue: ScratchCardViewModel(member: member, dataManager: DataManager.shared))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 刮除覆盖层
                if viewModel.showScratchOverlay, let selectedIndex = viewModel.selectedCardIndex {
                    scratchOverlay(cardIndex: selectedIndex)
                        .allowsHitTesting(true) // 禁用点击外面退回
                }
                
                // 结果弹窗
                if viewModel.showResult {
                    resultOverlay
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
                
                // 粒子效果层
                particleEffectLayer
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("scratch_card.title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadScratchCardConfig()
        }
        .environmentObject(dataManager)
        // 刮刮卡配置弹窗
        .overlay(
            ScratchCardConfigPopupView(
                isPresented: $showScratchCardConfigPopup,
                selectedMember: member,
                onSave: { configData in
                    handleScratchCardConfigSave(configData)
                },
                onCancel: {
                    showScratchCardConfigPopup = false
                }
            )
        )
    }
    
    // MARK: - Background Gradient

    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#ffeef0").opacity(0.3),
                Color(hex: "#fff8e1").opacity(0.2),
                Color.white
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Main Content

    private func mainContent(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // 顶部统计信息
            if !viewModel.cardItems.isEmpty {
                scratchCardStatsView
                    .padding(.top, 20)
                    .padding(.bottom, 16)
            }

            // 刮刮卡网格或空状态
            contentArea(geometry: geometry)
        }
    }
    
    // MARK: - Content Area

    @ViewBuilder
    private func contentArea(geometry: GeometryProxy) -> some View {
        if viewModel.isLoading {
            loadingView
        } else if viewModel.cardItems.isEmpty {
            emptyStateView
        } else {
            scratchCardGridView(geometry: geometry)
        }
    }
    
    // MARK: - Stats View

    private var scratchCardStatsView: some View {
        VStack(spacing: 12) {
            HStack {
                // 剩余卡片数
                HStack(spacing: 8) {
                    Image(systemName: "square.stack.3d.up")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#ff6b6b"))

                    Text(String(format: "scratch_card.remaining_format".localized, viewModel.unscatchedCount))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }

                Spacer()

                // 消耗积分
                HStack(spacing: 8) {
                    Image(systemName: "star.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "#a9d051"))

                    Text(String(format: "scratch_card.cost_format".localized, viewModel.costPerScratch))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }

            // 当前积分
            HStack(spacing: 8) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color.blue)

                Text(String(format: "scratch_card.current_points_format".localized, member.currentPoints))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 积分状态
                Text(viewModel.canAffordScratching ? "scratch_card.points_sufficient".localized : "scratch_card.points_insufficient".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(viewModel.canAffordScratching ? Color.green : Color.red)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill((viewModel.canAffordScratching ? Color.green : Color.red).opacity(0.1))
                    )
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.8))
                .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 20)
    }
    
    // MARK: - Loading View

    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: Color(hex: "#ff6b6b")))

            Text("scratch_card.loading".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Empty State View

    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "square.stack.3d.up")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(Color.gray.opacity(0.5))

            VStack(spacing: 8) {
                Text("scratch_card.no_config".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("scratch_card.config_description".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: {
                showScratchCardConfigPopup = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "gear")
                        .font(.system(size: 16, weight: .medium))

                    Text("scratch_card.go_settings".localized)
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(hex: "#ff6b6b"))
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Scratch Card Grid View

    private func scratchCardGridView(geometry: GeometryProxy) -> some View {
        ScratchCardGridView(
            viewModel: viewModel,
            geometry: geometry,
            onCardTapped: { index in
                handleCardTapped(at: index)
            }
        )
    }
    
    // MARK: - Back Button
    
    private var backButton: some View {
        Button(action: {
            onDismiss()
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))

                Text("scratch_card.back".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    
    // MARK: - Scratch Overlay
    
    private func scratchOverlay(cardIndex: Int) -> some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.8)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                // 添加"开心刮刮卡"标题
                VStack(spacing: 20) {
                    Text("scratch_card.happy_title".localized)
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)

                    Text("scratch_card.happy_subtitle".localized)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.top, 80)
                
                // 刮刮卡（缩小尺寸）
                ScratchCardCanvasView(
                    cardItem: viewModel.cardItems[cardIndex],
                    onProgressUpdate: { progress in
                        viewModel.updateScratchProgress(index: cardIndex, progress: progress)
                    },
                    onScratchComplete: {
                        viewModel.revealPrize(at: cardIndex)
                    }
                )
                .scaleEffect(1.2)
                
                Spacer()
            }
        }
    }
    
    // MARK: - Result Overlay
    
    private var resultOverlay: some View {
        ScratchCardResultView(
            prizeName: viewModel.resultPrize,
            costPoints: viewModel.costPerScratch,
            onConfirm: {
                viewModel.confirmResult()
            }
        )
    }
    
    // MARK: - Insufficient Points Alert
    
    private var insufficientPointsAlert: some View {
        ZStack {
            Color.black.opacity(0.6)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                Text("scratch_card.insufficient_points_title".localized)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(String(format: "scratch_card.insufficient_points_message_format".localized, viewModel.costPerScratch, member.currentPoints))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)

                Button(action: {
                    viewModel.dismissInsufficientPointsAlert()
                }) {
                    Text("common.button.confirm".localized)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 100, height: 44)
                        .background(Color(hex: "#ff6b6b"))
                        .clipShape(RoundedRectangle(cornerRadius: 22))
                }
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
        }
    }
    
    // MARK: - Particle Effect Layer

    private var particleEffectLayer: some View {
        ZStack {
            ForEach(viewModel.particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.life)
            }
        }
        .allowsHitTesting(false)
    }

    // MARK: - Action Handlers

    /**
     * 处理卡片点击
     */
    private func handleCardTapped(at index: Int) {
        let success = viewModel.selectCard(at: index)
        if success {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
    }

    /**
     * 处理刮刮卡配置保存
     */
    private func handleScratchCardConfigSave(_ configData: ScratchCardConfigData) {
        print("保存刮刮卡配置: 成员=\(member.name ?? "未知"), 卡片数=\(configData.cardCount), 积分=\(configData.costPerPlay)")
        print("奖品列表: \(configData.cardPrizes)")

        // 调用DataManager保存刮刮卡配置
        let savedConfig = dataManager.saveScratchCardConfig(
            for: member,
            cardCount: configData.cardCount,
            costPerPlay: configData.costPerPlay,
            cardPrizes: configData.cardPrizes
        )

        // 关闭弹窗
        showScratchCardConfigPopup = false

        // 根据保存结果重新加载配置
        if savedConfig != nil {
            // 保存成功，重新加载刮刮卡配置
            viewModel.loadScratchCardConfig()
            print("✅ 刮刮卡配置保存成功，已重新加载")
        } else {
            print("❌ 刮刮卡配置保存失败")
        }
    }
}

#Preview {
    NavigationView {
        ScratchCardView(
            member: Member(), // 需要提供一个示例Member
            onDismiss: {
                print("Dismissed")
            }
        )
    }
    .environmentObject(DataManager.shared)
}
