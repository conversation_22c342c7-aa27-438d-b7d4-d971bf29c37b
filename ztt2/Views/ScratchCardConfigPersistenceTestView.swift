//
//  ScratchCardConfigPersistenceTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡配置持久化测试视图
 * 用于验证刮刮卡配置的保存和加载功能
 */
struct ScratchCardConfigPersistenceTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var testMember: Member?
    @State private var showConfigPopup = false
    @State private var testResults: [String] = []
    @State private var isTestRunning = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("刮刮卡配置持久化测试")
                    .font(.title)
                    .fontWeight(.bold)
                
                if let member = testMember {
                    VStack(spacing: 15) {
                        Text("测试成员: \(member.name ?? "未知")")
                            .font(.headline)
                        
                        // 显示当前配置状态
                        if let config = member.getLotteryConfig(for: .scratchcard) {
                            VStack(alignment: .leading, spacing: 5) {
                                Text("当前配置:")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                Text("卡片数量: \(config.itemCount)")
                                Text("每次消耗积分: \(config.costPerPlay)")
                                Text("奖品列表:")
                                ForEach(config.allItems, id: \.id) { item in
                                    Text("  \(item.itemIndex + 1). \(item.formattedPrizeName)")
                                        .font(.caption)
                                }
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        } else {
                            Text("暂无配置")
                                .foregroundColor(.gray)
                        }
                        
                        // 测试按钮
                        VStack(spacing: 10) {
                            Button("打开配置弹窗") {
                                showConfigPopup = true
                            }
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            
                            Button("运行自动化测试") {
                                runAutomatedTest()
                            }
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            .disabled(isTestRunning)
                            
                            Button("清除配置") {
                                clearConfig()
                            }
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                    }
                } else {
                    Button("创建测试成员") {
                        createTestMember()
                    }
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                
                // 测试结果显示
                if !testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("测试结果:")
                            .font(.headline)
                        
                        ScrollView {
                            VStack(alignment: .leading, spacing: 3) {
                                ForEach(testResults, id: \.self) { result in
                                    Text(result)
                                        .font(.caption)
                                        .foregroundColor(result.contains("✅") ? .green : 
                                                       result.contains("❌") ? .red : .primary)
                                }
                            }
                        }
                        .frame(maxHeight: 200)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("配置持久化测试")
            .onAppear {
                setupTestData()
            }
        }
        .overlay(
            ScratchCardConfigPopupView(
                isPresented: $showConfigPopup,
                selectedMember: testMember,
                onSave: { configData in
                    handleConfigSave(configData)
                },
                onCancel: {
                    showConfigPopup = false
                    addTestResult("❌ 用户取消了配置")
                }
            )
        )
    }
    
    // MARK: - 测试方法
    
    private func setupTestData() {
        createTestMember()
    }
    
    private func createTestMember() {
        let context = dataManager.persistenceController.container.viewContext
        
        // 查找现有测试成员
        let request = Member.fetchRequest()
        request.predicate = NSPredicate(format: "name == %@", "配置测试成员")
        
        do {
            let members = try context.fetch(request)
            if let existingMember = members.first {
                testMember = existingMember
                addTestResult("✅ 找到现有测试成员")
                return
            }
        } catch {
            addTestResult("❌ 查找测试成员失败: \(error)")
        }
        
        // 创建新的测试成员
        let member = Member(context: context)
        member.id = UUID()
        member.name = "配置测试成员"
        member.role = "儿子"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -10, to: Date())
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        
        dataManager.save()
        testMember = member
        addTestResult("✅ 创建新测试成员成功")
    }
    
    private func handleConfigSave(_ configData: ScratchCardConfigData) {
        guard let member = testMember else {
            addTestResult("❌ 测试成员不存在")
            return
        }
        
        addTestResult("📝 保存配置: 卡片数=\(configData.cardCount), 积分=\(configData.costPerPlay)")
        addTestResult("📝 奖品列表: \(configData.cardPrizes)")
        
        // 保存配置
        let savedConfig = dataManager.saveScratchCardConfig(
            for: member,
            cardCount: configData.cardCount,
            costPerPlay: configData.costPerPlay,
            cardPrizes: configData.cardPrizes
        )
        
        showConfigPopup = false
        
        if savedConfig != nil {
            addTestResult("✅ 配置保存成功")
            
            // 验证配置是否正确保存
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                verifyConfigPersistence(expectedData: configData)
            }
        } else {
            addTestResult("❌ 配置保存失败")
        }
    }
    
    private func verifyConfigPersistence(expectedData: ScratchCardConfigData) {
        guard let member = testMember,
              let config = member.getLotteryConfig(for: .scratchcard) else {
            addTestResult("❌ 无法获取保存的配置")
            return
        }
        
        // 验证基本配置
        let cardCountMatch = Int(config.itemCount) == expectedData.cardCount
        let costMatch = Int(config.costPerPlay) == expectedData.costPerPlay
        
        addTestResult(cardCountMatch ? "✅ 卡片数量匹配" : "❌ 卡片数量不匹配")
        addTestResult(costMatch ? "✅ 积分消耗匹配" : "❌ 积分消耗不匹配")
        
        // 验证奖品列表
        let savedPrizes = config.allItems.map { $0.formattedPrizeName }
        let prizesMatch = savedPrizes == expectedData.cardPrizes
        
        addTestResult(prizesMatch ? "✅ 奖品列表匹配" : "❌ 奖品列表不匹配")
        addTestResult("📝 保存的奖品: \(savedPrizes)")
        addTestResult("📝 期望的奖品: \(expectedData.cardPrizes)")
    }
    
    private func runAutomatedTest() {
        isTestRunning = true
        testResults.removeAll()
        addTestResult("🚀 开始自动化测试...")
        
        // 测试1: 清除现有配置
        clearConfig()
        
        // 测试2: 创建第一个配置
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let testConfig1 = ScratchCardConfigData(
                cardCount: 5,
                costPerPlay: 10,
                cardPrizes: ["奖品1", "奖品2", "奖品3", "奖品4", "奖品5"]
            )
            self.handleConfigSave(testConfig1)
            
            // 测试3: 修改配置
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                let testConfig2 = ScratchCardConfigData(
                    cardCount: 8,
                    costPerPlay: 15,
                    cardPrizes: ["新奖品1", "新奖品2", "新奖品3", "新奖品4", "新奖品5", "新奖品6", "新奖品7", "新奖品8"]
                )
                self.handleConfigSave(testConfig2)
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.addTestResult("🎉 自动化测试完成")
                    self.isTestRunning = false
                }
            }
        }
    }
    
    private func clearConfig() {
        guard let member = testMember else { return }
        
        let success = dataManager.deleteScratchCardConfig(for: member)
        addTestResult(success ? "✅ 配置清除成功" : "❌ 配置清除失败")
    }
    
    private func addTestResult(_ result: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        testResults.append("[\(timestamp)] \(result)")
    }
}

#Preview {
    ScratchCardConfigPersistenceTestView()
        .environmentObject(DataManager.shared)
}
