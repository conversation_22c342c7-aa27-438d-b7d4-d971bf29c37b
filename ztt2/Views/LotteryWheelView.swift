//
//  LotteryWheelView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/31.
//  基于ztt1项目的LotteryWheelView移植并适配到ztt2项目
//

import SwiftUI

/**
 * 大转盘抽奖组件
 * 采用分层架构设计，支持精确的文字放射性排列和动态装饰系统
 * 兼容iOS15.6以上版本
 */
struct LotteryWheelView: View {
    
    // MARK: - Properties
    
    @Binding var isPresented: Bool
    let member: Member
    let onLotteryComplete: (String, Int) -> Void
    let onDismiss: () -> Void
    let onNavigateToSettings: (() -> Void)?
    
    // MARK: - Environment
    @EnvironmentObject private var dataManager: DataManager
    
    // MARK: - State
    @State private var currentRotation: Double = 0
    @State private var isSpinning = false
    @State private var showResult = false
    @State private var resultPrize = ""
    @State private var showFireworks = false
    @State private var animationTrigger = false
    @State private var buttonScale: CGFloat = 1.0
    @State private var wheelConfig: LotteryConfig?
    @State private var prizeItems: [LotteryItem] = []
    @State private var showInsufficientPoints = false
    @State private var showNoConfig = false
    @State private var showWheelConfig = false
    
    // MARK: - Constants
    private func wheelSize(for geometry: GeometryProxy) -> CGFloat {
        return geometry.size.width * 0.8
    }
    
    private func wheelRadius(for geometry: GeometryProxy) -> CGFloat {
        return wheelSize(for: geometry) / 2
    }
    private let centerButtonSize: CGFloat = 60
    
    private func textRadius(for geometry: GeometryProxy) -> CGFloat {
        return wheelRadius(for: geometry) * 0.67  // 大约是半径的2/3
    }
    
    private func decorationRadius(for geometry: GeometryProxy) -> CGFloat {
        return wheelRadius(for: geometry) * 1.0  // 超出转盘外边缘
    }
    private let pointerHeight: CGFloat = 30
    
    // MARK: - Computed Properties
    
    private var canAffordLottery: Bool {
        guard let config = wheelConfig else { return false }
        return member.currentPoints >= config.costPerPlay
    }
    
    private var costPerPlay: Int {
        return Int(wheelConfig?.costPerPlay ?? 5)
    }
    
    private var sectionAngle: Double {
        guard !prizeItems.isEmpty else { return 0 }
        return 360.0 / Double(prizeItems.count)
    }
    
    private var wheelColors: [Color] {
        return [
            Color(hex: "#FF7B7B"), Color(hex: "#FFD166"),
            Color(hex: "#06FFA5"), Color(hex: "#4ECDC4"),
            Color(hex: "#45B7D1"), Color(hex: "#96CEB4"),
            Color(hex: "#FF9FF3"), Color(hex: "#54A0FF"),
            Color(hex: "#5F27CD"), Color(hex: "#FF9F43"),
            Color(hex: "#10AC84"), Color(hex: "#EE5A24")
        ]
    }
    
    private var decorationCount: Int {
        return max(16, prizeItems.count * 2)
    }
    
    private var decorationAngle: Double {
        return 360.0 / Double(decorationCount)
    }
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 白色背景
                Color.white
                    .ignoresSafeArea()
                
                // 主要内容
                VStack(spacing: 0) {
                    // 头部
                    headerView
                    
                    if wheelConfig == nil {
                        noConfigView
                    } else if prizeItems.isEmpty {
                        emptyPrizesView
                    } else {
                        wheelContentView(geometry: geometry)
                    }
                    
                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.clear)
                
                // 覆盖层
                if showFireworks { fireworksOverlay }
                if showResult { resultOverlay }
                if showInsufficientPoints { insufficientPointsAlert }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("lottery_wheel.page_title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            loadWheelConfiguration()
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationTrigger = true
            }
        }
        .onChange(of: isPresented) { newValue in
            if !newValue {
                animationTrigger = false
                resetWheel()
            }
        }
        // 大转盘配置弹窗
        .overlay(
            WheelConfigPopupView(
                isPresented: $showWheelConfig,
                selectedMember: member,
                onSave: { configData in
                    handleWheelConfigSave(configData)
                },
                onCancel: {
                    showWheelConfig = false
                }
            )
        )
    }
    
    // MARK: - Navigation Components
    
    /**
     * 返回按钮
     */
    private var backButton: some View {
        Button(action: {
            if !isSpinning {
                onDismiss()
            }
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))
                
                Text("lottery_wheel.back_button".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(DesignSystem.Colors.textPrimary)
        }
        .disabled(isSpinning)
    }
    
    // MARK: - View Components
    
    /**
     * 头部视图
     */
    private var headerView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 成员信息
            HStack(spacing: 16) {
                Image(member.avatarImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 28, height: 28)
                    .clipShape(Circle())

                VStack(alignment: .leading, spacing: 6) {
                    Text(member.displayName)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("lottery_wheel.current_points".localized(with: Int(member.currentPoints)))
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 未配置状态视图
     */
    private var noConfigView: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("lottery_wheel.no_config.title".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Text("lottery_wheel.no_config.description".localized)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
            
            // 添加"前往配置"按钮
            Button(action: {
                showWheelConfig = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "gear")
                        .font(.system(size: 14, weight: .medium))
                    
                    Text("lottery_wheel.go_settings".localized)
                        .font(.system(size: 14, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    LinearGradient(
                        colors: [Color(hex: "#a9d051"), Color(hex: "#8bb83f")],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(20)
                .shadow(color: Color(hex: "#a9d051").opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .padding(.top, 8)
        }
        .padding(.vertical, 40)
        .padding(.horizontal, 20)
    }
    
    /**
     * 空奖品状态视图
     */
    private var emptyPrizesView: some View {
        VStack(spacing: 16) {
            Image(systemName: "gift.circle")
                .font(.system(size: 48))
                .foregroundColor(.gray.opacity(0.5))

            Text("lottery_wheel.empty_prizes.title".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Text("lottery_wheel.empty_prizes.description".localized)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
        .padding(.vertical, 40)
        .padding(.horizontal, 20)
    }

    /**
     * 转盘内容视图
     */
    private func wheelContentView(geometry: GeometryProxy) -> some View {
        let currentWheelSize = wheelSize(for: geometry)

        return VStack(spacing: 30) {
            // 成本提示
            costInfoView

            // 添加顶部间距，将转盘向下移动
            Spacer()
                .frame(height: 10)

            // 转盘区域 - 分层架构
            ZStack {
                // 第1层：背景和装饰
                wheelBackgroundLayer(geometry: geometry)

                // 第2层：扇形（可旋转）
                wheelSectionLayer(geometry: geometry)
                    .rotationEffect(.degrees(currentRotation))
                    .animation(isSpinning ? .timingCurve(0.15, 0.05, 0.1, 1.0, duration: 8.0) : .default, value: currentRotation)

                // 第3层：文字（跟随扇形旋转）
                wheelTextLayer(geometry: geometry)
                    .rotationEffect(.degrees(currentRotation))
                    .animation(isSpinning ? .timingCurve(0.15, 0.05, 0.1, 1.0, duration: 8.0) : .default, value: currentRotation)

                // 第4层：固定指针（不旋转）
                wheelPointer(geometry: geometry)

                // 第5层：中心按钮（不旋转）
                wheelCenterButton
            }
            .frame(width: currentWheelSize, height: currentWheelSize)

            // 添加间距，将抽奖按钮向下移动
            Spacer()
                .frame(height: 15)

            // 抽奖按钮
            lotteryButton(geometry: geometry)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 40)
    }

    /**
     * 成本信息视图
     */
    private var costInfoView: some View {
        HStack(spacing: 8) {
            Image(systemName: "star.circle.fill")
                .font(.system(size: 16))
                .foregroundColor(.orange)

            Text("lottery_wheel.cost_info".localized(with: costPerPlay))
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textSecondary)

            Spacer()

            // 可用性指示器
            HStack(spacing: 4) {
                Circle()
                    .fill(canAffordLottery ? .green : .red)
                    .frame(width: 8, height: 8)

                Text(canAffordLottery ? "lottery_wheel.affordable".localized : "lottery_wheel.insufficient_points".localized)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(canAffordLottery ? .green : .red)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(hex: "#f8f8f8"))
        .cornerRadius(12)
    }

    /**
     * 转盘背景层
     */
    private func wheelBackgroundLayer(geometry: GeometryProxy) -> some View {
        let currentWheelSize = wheelSize(for: geometry)
        let currentWheelRadius = wheelRadius(for: geometry)
        let currentDecorationRadius = decorationRadius(for: geometry)

        return ZStack {
            // 外圈装饰圆环
            Circle()
                .stroke(
                    LinearGradient(
                        colors: [Color(hex: "#FF6B6B"), Color(hex: "#FF8E8E"), Color(hex: "#FFA0A0")],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 8
                )
                .frame(width: currentWheelSize, height: currentWheelSize)
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)

            // 装饰小圆点（动态数量，基于扇形数量）
            ForEach(0..<decorationCount, id: \.self) { index in
                Circle()
                    .fill(index % 2 == 0 ? Color.yellow : Color.white)
                    .frame(width: 8, height: 8)
                    .position(
                        x: currentWheelRadius + cos(Double(index) * decorationAngle * .pi / 180) * currentDecorationRadius,
                        y: currentWheelRadius + sin(Double(index) * decorationAngle * .pi / 180) * currentDecorationRadius
                    )
                    .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)
            }

            // 转盘主体背景
            Circle()
                .fill(
                    RadialGradient(
                        colors: [Color.white, Color.gray.opacity(0.1)],
                        center: .center,
                        startRadius: 0,
                        endRadius: currentWheelRadius * 0.93
                    )
                )
                .frame(width: currentWheelSize - 20, height: currentWheelSize - 20)
                .shadow(color: Color.black.opacity(0.15), radius: 10, x: 0, y: 5)
        }
    }

    /**
     * 转盘扇形绘制
     */
    private func wheelSection(for item: LotteryItem, at index: Int, geometry: GeometryProxy) -> some View {
        let startAngle = Double(index) * sectionAngle - 90
        let endAngle = startAngle + sectionAngle
        let color = wheelColors[index % wheelColors.count]
        let currentWheelSize = wheelSize(for: geometry)

        return WheelSectionShape(
            startAngle: Angle(degrees: startAngle),
            endAngle: Angle(degrees: endAngle)
        )
        .fill(color)
        .frame(width: currentWheelSize - 20, height: currentWheelSize - 20)
        .overlay(
            WheelSectionShape(
                startAngle: Angle(degrees: startAngle),
                endAngle: Angle(degrees: endAngle)
            )
            .stroke(Color.white.opacity(0.5), lineWidth: 2)
            .frame(width: currentWheelSize - 20, height: currentWheelSize - 20)
        )
    }

    /**
     * 径向奖品名称显示
     */
    private func wheelText(for item: LotteryItem, at index: Int, geometry: GeometryProxy) -> some View {
        let midAngle = Double(index) * sectionAngle - 90 + sectionAngle / 2
        let prizeText = item.formattedPrizeName.isEmpty ? "lottery_wheel.empty_prize".localized : item.formattedPrizeName
        let currentTextRadius = textRadius(for: geometry)
        let currentWheelRadius = wheelRadius(for: geometry)

        return PrizeLabelView(
            text: prizeText,
            angle: midAngle,
            radius: currentTextRadius,
            wheelCenter: CGPoint(x: currentWheelRadius, y: currentWheelRadius)
        )
    }

    /**
     * 转盘扇形层
     */
    private func wheelSectionLayer(geometry: GeometryProxy) -> some View {
        ZStack {
            // 转盘分区
            ForEach(Array(prizeItems.enumerated()), id: \.element.id) { index, item in
                wheelSection(for: item, at: index, geometry: geometry)
            }
        }
    }

    /**
     * 转盘文字层
     */
    private func wheelTextLayer(geometry: GeometryProxy) -> some View {
        ZStack {
            // 奖品文字（跟随扇形旋转）
            ForEach(Array(prizeItems.enumerated()), id: \.element.id) { index, item in
                wheelText(for: item, at: index, geometry: geometry)
            }
        }
    }

    /**
     * 固定在顶部的指针（不随转盘旋转）
     */
    private func wheelPointer(geometry: GeometryProxy) -> some View {
        let currentWheelSize = wheelSize(for: geometry)

        return VStack {
            // 指针三角形
            ZStack {
                // 指针阴影
                Triangle()
                    .fill(Color.black.opacity(0.3))
                    .frame(width: pointerHeight, height: pointerHeight)
                    .offset(x: 1, y: 1)

                // 指针主体
                Triangle()
                    .fill(
                        LinearGradient(
                            colors: [Color(hex: "#FF3B3B"), Color(hex: "#FF6B6B")],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: pointerHeight, height: pointerHeight)

                // 指针边框
                Triangle()
                    .stroke(Color.white, lineWidth: 2)
                    .frame(width: pointerHeight, height: pointerHeight)
            }
            .rotationEffect(.degrees(180)) // 旋转180度让指针朝下
            .offset(y: -20) // 确保指针在转盘边缘之外

            Spacer()
        }
        .frame(height: currentWheelSize)
    }

    /**
     * 中心装饰按钮
     */
    private var wheelCenterButton: some View {
        ZStack {
            Circle()
                .fill(
                    RadialGradient(
                        colors: [Color(hex: "#FFD700"), Color(hex: "#FFA500")],
                        center: .center,
                        startRadius: 0,
                        endRadius: centerButtonSize / 2
                    )
                )
                .frame(width: centerButtonSize, height: centerButtonSize)
                .shadow(color: Color.black.opacity(0.3), radius: 5, x: 0, y: 2)

            Circle()
                .stroke(Color.white, lineWidth: 3)
                .frame(width: centerButtonSize, height: centerButtonSize)

            Text("GO")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.white)
                .shadow(color: Color.black.opacity(0.5), radius: 1, x: 0, y: 1)
        }
    }

    /**
     * 抽奖按钮
     */
    private func lotteryButton(geometry: GeometryProxy) -> some View {
        let buttonWidth = geometry.size.width * 0.7

        return Button(action: {
            if canAffordLottery {
                spinWheel()
            } else {
                showInsufficientPointsAlert()
            }
        }) {
            HStack(spacing: 12) {
                if isSpinning {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                }

                Text(isSpinning ? "lottery_wheel.spinning".localized : "lottery_wheel.spin_button".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
            }
            .frame(width: buttonWidth)
            .frame(height: 50)
            .background(
                LinearGradient(
                    colors: canAffordLottery ?
                        [Color(hex: "#FF6B6B"), Color(hex: "#FF8E8E")] :
                        [Color.gray.opacity(0.5), Color.gray.opacity(0.3)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(25)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        }
        .scaleEffect(buttonScale)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                buttonScale = pressing ? 0.95 : 1.0
            }
        }, perform: {})
        .disabled(isSpinning || !canAffordLottery)
    }

    /**
     * 烟花动画覆盖层
     */
    private var fireworksOverlay: some View {
        ZStack {
            // 多个烟花效果
            ForEach(0..<6, id: \.self) { index in
                fireworksParticle(index: index)
            }

            // 烟花图片效果（使用系统图标替代）
            ForEach(0..<3, id: \.self) { index in
                Image(systemName: "sparkles")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 60, height: 60)
                    .foregroundColor(.yellow)
                    .position(
                        x: CGFloat.random(in: 100...300),
                        y: CGFloat.random(in: 150...400)
                    )
                    .opacity(showFireworks ? 1.0 : 0.0)
                    .scaleEffect(showFireworks ? 1.2 : 0.3)
                    .rotationEffect(.degrees(showFireworks ? 360 : 0))
                    .animation(.easeInOut(duration: 1.5).delay(Double(index) * 0.3), value: showFireworks)
            }
        }
        .ignoresSafeArea()
    }

    /**
     * 烟花粒子效果
     */
    private func fireworksParticle(index: Int) -> some View {
        Circle()
            .fill(
                LinearGradient(
                    colors: [Color.yellow, Color.orange, Color.red],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: 8, height: 8)
            .position(
                x: CGFloat.random(in: 50...350),
                y: CGFloat.random(in: 100...500)
            )
            .opacity(showFireworks ? 1.0 : 0.0)
            .scaleEffect(showFireworks ? 1.0 : 0.1)
            .animation(
                .easeOut(duration: 1.0)
                .delay(Double(index) * 0.1)
                .repeatCount(3, autoreverses: false),
                value: showFireworks
            )
    }

    /**
     * 结果展示覆盖层
     */
    private var resultOverlay: some View {
        ZStack {
            Color.black.opacity(0.6)
                .ignoresSafeArea()

            VStack(spacing: 30) {
                // 庆祝图标
                Image(systemName: "party.popper.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.yellow)
                    .shadow(color: Color.black.opacity(0.3), radius: 3, x: 0, y: 2)

                // 中奖奖品名称
                Text(resultPrize)
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
                    .shadow(color: Color.black.opacity(0.5), radius: 3, x: 0, y: 2)
                    .padding(.horizontal, 20)

                // 确认按钮
                Button(action: {
                    confirmResult()
                }) {
                    Text("lottery_wheel.confirm_result".localized)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 220, height: 50)
                        .background(
                            LinearGradient(
                                colors: [Color.green, Color.green.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .cornerRadius(25)
                        .shadow(color: Color.black.opacity(0.3), radius: 5, x: 0, y: 3)
                }
            }
            .padding(.horizontal, 40)
        }
    }

    /**
     * 积分不足提示
     */
    private var insufficientPointsAlert: some View {
        ZStack {
            Color.black.opacity(0.4)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.orange)

                Text("lottery_wheel.insufficient_points_title".localized)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("lottery_wheel.insufficient_points_message".localized(with: "\(costPerPlay)", "\(Int(member.currentPoints))"))
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)

                Button("common.button.confirm".localized) {
                    showInsufficientPoints = false
                }
                .buttonStyle(.borderedProminent)
            }
            .padding(.horizontal, 40)
            .padding(.vertical, 30)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
    }

    // MARK: - Helper Methods

    /**
     * 处理大转盘配置保存
     */
    private func handleWheelConfigSave(_ configData: WheelConfigData) {
        print("🎯 保存大转盘配置: 分区数=\(configData.sectorCount), 积分=\(configData.costPerPlay)")

        // 创建或更新配置
        let context = dataManager.persistenceController.container.viewContext
        let _ = member.createOrUpdateLotteryConfig(
            toolType: .wheel,
            itemCount: configData.sectorCount,
            costPerPlay: configData.costPerPlay,
            prizeNames: configData.sectorPrizes,
            in: context
        )

        // 保存到数据库
        dataManager.save()

        // 关闭弹窗
        showWheelConfig = false

        // 重新加载配置
        loadWheelConfiguration()

        print("✅ 大转盘配置保存成功")
    }

    /**
     * 加载转盘配置
     */
    private func loadWheelConfiguration() {
        // 获取成员的大转盘配置
        if let config = member.getLotteryConfig(for: .wheel) {
            // 验证配置完整性
            if validateConfiguration(config) {
                wheelConfig = config
                prizeItems = config.allItems

                print("✅ 加载大转盘配置成功: 分区数=\(config.itemCount), 积分=\(config.costPerPlay)")
                print("📋 奖品列表: \(prizeItems.map { $0.formattedPrizeName })")
            } else {
                // 配置不完整，清空配置
                wheelConfig = nil
                prizeItems = []
                print("⚠️ 大转盘配置不完整，已清空配置")
            }
        } else {
            wheelConfig = nil
            prizeItems = []
            print("⚠️ 未找到大转盘配置，请先进行配置")
        }
    }

    /**
     * 验证配置完整性
     */
    private func validateConfiguration(_ config: LotteryConfig) -> Bool {
        // 检查基本配置
        guard config.itemCount >= 4 && config.itemCount <= 12 else {
            print("❌ 配置验证失败: 分区数量超出范围 \(config.itemCount)")
            return false
        }

        guard config.costPerPlay >= 0 else {
            print("❌ 配置验证失败: 积分设置无效 \(config.costPerPlay)")
            return false
        }

        // 检查奖品项目
        let items = config.allItems
        guard items.count == config.itemCount else {
            print("❌ 配置验证失败: 奖品数量与分区数量不匹配 奖品=\(items.count), 分区=\(config.itemCount)")
            return false
        }

        // 检查奖品名称
        for item in items {
            let prizeName = item.formattedPrizeName.trimmingCharacters(in: .whitespacesAndNewlines)
            if prizeName.isEmpty {
                print("❌ 配置验证失败: 发现空奖品名称 分区=\(item.itemIndex)")
                return false
            }
        }

        print("✅ 配置验证通过")
        return true
    }

    /**
     * 精确旋转转盘算法
     */
    private func spinWheel() {
        guard !prizeItems.isEmpty else { return }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()

        isSpinning = true

        // 随机生成最终停止角度
        let randomStopAngle = Double.random(in: 0..<360)
        let randomOffset = Double.random(in: -5...5)
        let finalStopAngle = (randomStopAngle + randomOffset).truncatingRemainder(dividingBy: 360)

        // 新的旋转逻辑 - 确保始终顺时针旋转
        let baseSpins = Double.random(in: 6...10) * 360  // 6-10圈顺时针基础旋转
        let targetAngleOffset = finalStopAngle

        // 计算到达目标位置需要的额外角度
        let currentNormalizedAngle = currentRotation.truncatingRemainder(dividingBy: 360)
        let targetNormalizedAngle = targetAngleOffset.truncatingRemainder(dividingBy: 360)
        let additionalRotation = targetNormalizedAngle >= currentNormalizedAngle ?
            (targetNormalizedAngle - currentNormalizedAngle) :
            (360 - currentNormalizedAngle + targetNormalizedAngle)

        // 最终旋转角度
        let finalRotation = currentRotation + baseSpins + additionalRotation

        // 执行旋转动画
        withAnimation(.timingCurve(0.15, 0.05, 0.1, 1.0, duration: 8.0)) {
            currentRotation = finalRotation
        }

        // 延迟显示结果
        DispatchQueue.main.asyncAfter(deadline: .now() + 8.5) {
            // 根据最终角度计算中奖结果
            let winnerIndex = calculateWinnerIndex(from: finalRotation)
            let selectedPrize = prizeItems[winnerIndex]
            resultPrize = selectedPrize.formattedPrizeName.isEmpty ? "lottery_wheel.mystery_prize".localized : selectedPrize.formattedPrizeName

            isSpinning = false
            showResult = true

            // 显示烟花效果
            withAnimation(.easeInOut(duration: 0.5)) {
                showFireworks = true
            }

            // 几秒后自动隐藏烟花
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                withAnimation(.easeInOut(duration: 0.5)) {
                    showFireworks = false
                }
            }
        }
    }

    /**
     * 根据最终旋转角度计算中奖扇形索引
     */
    private func calculateWinnerIndex(from finalRotation: Double) -> Int {
        // 标准化角度到0-360范围
        let normalizedRotation = finalRotation.truncatingRemainder(dividingBy: 360)

        // 指针相对于转盘的角度
        let pointerAngleInSectors = (-normalizedRotation).truncatingRemainder(dividingBy: 360)

        // 处理负数角度，转换为正数
        let positivePointerAngle = pointerAngleInSectors < 0 ? pointerAngleInSectors + 360 : pointerAngleInSectors

        // 计算指针指向的扇形索引
        let winnerIndex = Int(positivePointerAngle / sectionAngle) % prizeItems.count

        print("🎯 转盘中奖计算: 最终角度=\(finalRotation), 中奖索引=\(winnerIndex), 奖品=\(prizeItems[winnerIndex].formattedPrizeName)")

        return winnerIndex
    }

    /**
     * 确认结果
     */
    private func confirmResult() {
        guard let config = wheelConfig else { return }

        // 保存抽奖记录和扣除积分
        let success = saveLotteryResult(config: config, prizeName: resultPrize)

        if success {
            print("✅ 抽奖记录保存成功: 奖品=\(resultPrize), 消耗积分=\(costPerPlay)")

            // 调用回调，传递中奖奖品和消耗积分
            onLotteryComplete(resultPrize, costPerPlay)

            // 重新加载配置（刷新成员积分等状态）
            loadWheelConfiguration()
        } else {
            print("❌ 抽奖记录保存失败")
            // 即使保存失败，也要调用回调让界面知道抽奖完成
            onLotteryComplete(resultPrize, costPerPlay)
        }

        showResult = false
        showFireworks = false
    }

    /**
     * 保存抽奖结果
     */
    private func saveLotteryResult(config: LotteryConfig, prizeName: String) -> Bool {
        // 检查积分是否足够
        guard member.currentPoints >= config.costPerPlay else {
            print("❌ 积分不足，无法完成抽奖")
            return false
        }

        // 扣除积分
        member.currentPoints -= config.costPerPlay
        member.updatedAt = Date()

        // 创建积分记录（记录积分扣除）
        dataManager.createPointRecord(
            for: member,
            value: -Int32(config.costPerPlay),
            reason: "大转盘抽奖",
            recordType: "lottery"
        )

        // 创建抽奖记录（直接显示在兑换记录中）
        dataManager.createLotteryRecord(
            for: member,
            toolType: config.toolType ?? "wheel",
            prizeResult: prizeName,
            cost: config.costPerPlay
        )

        // 保存所有更改
        dataManager.save()

        return true
    }

    /**
     * 显示积分不足提示
     */
    private func showInsufficientPointsAlert() {
        // 触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.warning)

        showInsufficientPoints = true
    }

    /**
     * 重置转盘状态
     */
    private func resetWheel() {
        isSpinning = false
        showResult = false
        showFireworks = false
        showInsufficientPoints = false
        showNoConfig = false
        resultPrize = ""
        buttonScale = 1.0
    }
}

/**
 * 转盘分区形状
 */
struct WheelSectionShape: Shape {
    let startAngle: Angle
    let endAngle: Angle

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2

        path.move(to: center)
        path.addArc(center: center, radius: radius, startAngle: startAngle, endAngle: endAngle, clockwise: false)
        path.closeSubpath()

        return path
    }
}

/**
 * 三角形指针形状
 */
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()

        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.midX, y: rect.minY))

        return path
    }
}

/**
 * 奖品标签视图 - 径向文字排列
 */
struct PrizeLabelView: View {
    let text: String
    let angle: Double  // 扇形中心角度
    let radius: CGFloat  // 文字距离中心的半径
    let wheelCenter: CGPoint  // 转盘中心点

    // MARK: - Computed Properties

    /// 文字旋转角度 - 确保从外向内自然阅读
    private var textRotationAngle: Double {
        let normalizedAngle = (angle + 180).truncatingRemainder(dividingBy: 360)

        // 避免文字倒立显示
        if normalizedAngle > 90 && normalizedAngle < 270 {
            return angle + 360
        } else {
            return angle + 180
        }
    }

    /// 文字在转盘中的位置
    private var textPosition: CGPoint {
        let radians = CGFloat(angle * .pi / 180)
        return CGPoint(
            x: wheelCenter.x + cos(radians) * radius,
            y: wheelCenter.y + sin(radians) * radius
        )
    }

    /// 根据文字长度自适应字号
    private var adaptiveFontSize: CGFloat {
        switch text.count {
        case 1...2:
            return 16
        case 3...4:
            return 14
        case 5...6:
            return 12
        default:
            return 10
        }
    }

    // MARK: - Body

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 卡通风格文字背景
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white.opacity(0.2))
                    .frame(width: max(CGFloat(text.count) * 12, 30), height: 20)
                    .blur(radius: 0.5)

                // 主要文字内容
                Text(text)
                    .font(.system(size: adaptiveFontSize, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(1)
                    .minimumScaleFactor(0.6)
                    .shadow(color: Color.black.opacity(0.8), radius: 2, x: 1, y: 1)
                    .shadow(color: Color.black.opacity(0.4), radius: 1, x: 0, y: 0)
            }
            .rotationEffect(.degrees(textRotationAngle))
            .position(textPosition)
        }
    }
}
