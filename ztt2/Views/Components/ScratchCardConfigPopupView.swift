//
//  ScratchCardConfigPopupView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡配置弹窗组件
 * 支持设置刮刮卡数量（2-20个）、每张刮刮卡的奖品名称输入、每次消耗积分设置
 */
struct ScratchCardConfigPopupView: View {
    
    @Binding var isPresented: Bool
    let selectedMember: Member?
    let onSave: (ScratchCardConfigData) -> Void
    let onCancel: () -> Void
    
    @State private var cardCount: Int = 5
    @State private var costPerPlay: String = "10"
    @State private var cardPrizes: [String] = []
    @State private var showValidationErrors = false
    @State private var validationErrors: [String] = []
    
    private let minCardCount = 2
    private let maxCardCount = 20
    
    var body: some View {
        LotteryConfigPopupView(
            isPresented: $isPresented,
            title: "lottery_config.scratchcard.title".localized,
            onSave: handleSave,
            onCancel: onCancel
        ) {
            VStack(spacing: 20) {
                // 成员信息显示
                if let member = selectedMember {
                    memberInfoSection(member: member)
                }
                
                // 每次消耗积分设置
                ConfigInputField(
                    title: "lottery_config.cost_per_play".localized,
                    placeholder: "lottery_config.cost_per_play.placeholder".localized,
                    text: $costPerPlay,
                    keyboardType: .numberPad
                )
                
                // 刮刮卡数量设置
                CountSelectorView(
                    title: "lottery_config.scratchcard.count".localized,
                    range: "lottery_config.scratchcard.count.range".localized,
                    count: $cardCount,
                    minCount: minCardCount,
                    maxCount: maxCardCount
                )
                .onChange(of: cardCount) { newCount in
                    updateCardPrizes(count: newCount)
                }
                
                // 刮刮卡奖品设置
                prizesSection
                
                // 验证错误显示
                if showValidationErrors && !validationErrors.isEmpty {
                    validationErrorsSection
                }
            }
        }
        .onAppear {
            setupInitialData()
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 成员信息显示区域
     */
    private func memberInfoSection(member: Member) -> some View {
        HStack {
            // 成员头像
            Image(member.avatarImageName)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 40, height: 40)
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 2) {
                Text(member.name ?? "未知成员")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("当前积分：\(member.currentPoints)")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(hex: "#f8ffe5"))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(hex: "#a9d051").opacity(0.2), lineWidth: 1)
        )
    }
    
    /**
     * 奖品设置区域
     */
    private var prizesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("lottery_config.prizes".localized)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            LazyVStack(spacing: 8) {
                ForEach(0..<cardCount, id: \.self) { index in
                    prizeInputRow(index: index)
                }
            }
        }
    }
    
    /**
     * 单个奖品输入行
     */
    private func prizeInputRow(index: Int) -> some View {
        HStack(spacing: 12) {
            // 刮刮卡标签
            Text(String(format: "lottery_config.scratchcard.item_format".localized, index + 1))
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .frame(width: 70, alignment: .leading)
            
            // 奖品输入框
            TextField("lottery_config.scratchcard.prize".localized, text: Binding(
                get: { 
                    index < cardPrizes.count ? cardPrizes[index] : ""
                },
                set: { newValue in
                    if index < cardPrizes.count {
                        cardPrizes[index] = newValue
                    }
                }
            ))
            .font(.system(size: 16))
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.white)
            .cornerRadius(6)
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
            )
        }
    }
    
    /**
     * 验证错误显示区域
     */
    private var validationErrorsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(validationErrors, id: \.self) { error in
                HStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.red)
                    
                    Text(error)
                        .font(.system(size: 14))
                        .foregroundColor(.red)
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.red.opacity(0.1))
        .cornerRadius(8)
    }
    
    // MARK: - 数据处理方法
    
    /**
     * 初始化数据
     */
    private func setupInitialData() {
        // 加载已有的刮刮卡配置
        if let member = selectedMember,
           let existingConfig = member.getLotteryConfig(for: .scratchcard) {
            // 加载现有配置数据
            cardCount = Int(existingConfig.itemCount)
            costPerPlay = String(existingConfig.costPerPlay)

            // 加载现有奖品数据
            let existingPrizes = existingConfig.allItems.map { $0.formattedPrizeName }
            cardPrizes = existingPrizes

            print("✅ 加载已有刮刮卡配置: 卡片数=\(cardCount), 积分=\(costPerPlay), 奖品=\(existingPrizes)")
        } else {
            // 没有现有配置，使用默认值
            updateCardPrizes(count: cardCount)
            print("📝 使用默认刮刮卡配置: 卡片数=\(cardCount), 积分=\(costPerPlay)")
        }
    }
    
    /**
     * 更新刮刮卡奖品数组
     */
    private func updateCardPrizes(count: Int) {
        if cardPrizes.count < count {
            // 增加奖品项
            cardPrizes.append(contentsOf: Array(repeating: "", count: count - cardPrizes.count))
        } else if cardPrizes.count > count {
            // 减少奖品项
            cardPrizes = Array(cardPrizes.prefix(count))
        }
    }
    
    /**
     * 验证表单数据
     */
    private func validateForm() -> Bool {
        validationErrors.removeAll()
        
        // 验证积分设置
        if costPerPlay.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            validationErrors.append("lottery_config.validation.invalid_cost".localized)
        } else if let cost = Int(costPerPlay), cost <= 0 {
            validationErrors.append("lottery_config.validation.cost_range".localized)
        } else if Int(costPerPlay) == nil {
            validationErrors.append("lottery_config.validation.invalid_cost".localized)
        }
        
        // 验证奖品名称
        let emptyPrizes = cardPrizes.enumerated().filter { $0.element.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
        if !emptyPrizes.isEmpty {
            validationErrors.append("lottery_config.validation.empty_prize".localized)
        }
        
        // 验证奖品名称长度
        let longPrizes = cardPrizes.filter { $0.trimmingCharacters(in: .whitespacesAndNewlines).count > 20 }
        if !longPrizes.isEmpty {
            validationErrors.append("lottery_config.validation.prize_too_long".localized)
        }
        
        showValidationErrors = !validationErrors.isEmpty
        return validationErrors.isEmpty
    }
    
    /**
     * 处理保存操作
     */
    private func handleSave() {
        guard validateForm() else { return }
        
        let configData = ScratchCardConfigData(
            cardCount: cardCount,
            costPerPlay: Int(costPerPlay) ?? 0,
            cardPrizes: cardPrizes.map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
        )
        
        onSave(configData)
    }
}

/**
 * 刮刮卡配置数据模型
 */
struct ScratchCardConfigData {
    let cardCount: Int
    let costPerPlay: Int
    let cardPrizes: [String]
}

#Preview {
    ScratchCardConfigPopupView(
        isPresented: .constant(true),
        selectedMember: nil,
        onSave: { _ in print("保存刮刮卡配置") },
        onCancel: { print("取消配置") }
    )
}
