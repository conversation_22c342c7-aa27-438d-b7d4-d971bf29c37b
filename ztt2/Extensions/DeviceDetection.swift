//
//  DeviceDetection.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import UIKit
import SwiftUI

/**
 * 设备检测工具类
 * 用于识别不同设备类型并提供相应的布局参数
 */
class DeviceDetection {
    
    /// 设备类型枚举
    enum DeviceType {
        case iphone
        case ipad
        case unknown
    }
    
    /// 屏幕尺寸类型
    enum ScreenSize {
        case compact    // iPhone SE, iPhone 12 mini等
        case regular    // iPhone 12, iPhone 13等
        case large      // iPhone 12 Pro Max, iPhone 13 Pro Max等
        case ipadMini   // iPad mini
        case ipadRegular // iPad 9.7", iPad 10.2"
        case ipadAir    // iPad Air
        case ipadPro11  // iPad Pro 11"
        case ipadPro12  // iPad Pro 12.9"
    }
    
    /// 获取当前设备类型
    static var deviceType: DeviceType {
        if UIDevice.current.userInterfaceIdiom == .phone {
            return .iphone
        } else if UIDevice.current.userInterfaceIdiom == .pad {
            return .ipad
        } else {
            return .unknown
        }
    }
    
    /// 是否为iPad
    static var isPad: Bool {
        return deviceType == .ipad
    }
    
    /// 是否为iPhone
    static var isPhone: Bool {
        return deviceType == .iphone
    }
    
    /// 获取屏幕尺寸
    static var screenSize: ScreenSize {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        let minDimension = min(screenWidth, screenHeight)
        let maxDimension = max(screenWidth, screenHeight)
        
        if isPad {
            // iPad尺寸判断
            if maxDimension >= 1366 { // iPad Pro 12.9"
                return .ipadPro12
            } else if maxDimension >= 1194 { // iPad Pro 11"
                return .ipadPro11
            } else if maxDimension >= 1180 { // iPad Air
                return .ipadAir
            } else if maxDimension >= 1024 { // iPad 9.7", iPad 10.2"
                return .ipadRegular
            } else { // iPad mini
                return .ipadMini
            }
        } else {
            // iPhone尺寸判断
            if minDimension >= 428 { // iPhone 12 Pro Max等
                return .large
            } else if minDimension >= 375 { // iPhone 12, iPhone 13等
                return .regular
            } else { // iPhone SE, iPhone 12 mini等
                return .compact
            }
        }
    }
    
    /// 获取屏幕宽度
    static var screenWidth: CGFloat {
        return UIScreen.main.bounds.width
    }
    
    /// 获取屏幕高度
    static var screenHeight: CGFloat {
        return UIScreen.main.bounds.height
    }
    
    /// 获取安全区域
    static var safeAreaInsets: UIEdgeInsets {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            return window.safeAreaInsets
        }
        return UIEdgeInsets.zero
    }
    
    /// 是否为横屏
    static var isLandscape: Bool {
        return screenWidth > screenHeight
    }
    
    /// 是否为竖屏
    static var isPortrait: Bool {
        return screenHeight > screenWidth
    }
    
    /// 获取设备描述信息
    static var deviceDescription: String {
        let type = deviceType == .ipad ? "iPad" : "iPhone"
        let size = screenSize
        return "\(type) - \(size)"
    }
}

/**
 * SwiftUI环境值扩展
 * 提供设备检测信息的便捷访问
 */
extension EnvironmentValues {
    var deviceType: DeviceDetection.DeviceType {
        get { self[DeviceTypeKey.self] }
        set { self[DeviceTypeKey.self] = newValue }
    }
    
    var screenSize: DeviceDetection.ScreenSize {
        get { self[ScreenSizeKey.self] }
        set { self[ScreenSizeKey.self] = newValue }
    }
    
    var isPad: Bool {
        get { self[IsPadKey.self] }
        set { self[IsPadKey.self] = newValue }
    }
}

private struct DeviceTypeKey: EnvironmentKey {
    static let defaultValue: DeviceDetection.DeviceType = DeviceDetection.deviceType
}

private struct ScreenSizeKey: EnvironmentKey {
    static let defaultValue: DeviceDetection.ScreenSize = DeviceDetection.screenSize
}

private struct IsPadKey: EnvironmentKey {
    static let defaultValue: Bool = DeviceDetection.isPad
}

/**
 * SwiftUI View扩展
 * 提供设备检测的便捷方法
 */
extension View {
    /// 根据设备类型应用不同的修饰符
    func adaptForDevice<Content: View>(
        iphone: @escaping () -> Content,
        ipad: @escaping () -> Content
    ) -> some View {
        Group {
            if DeviceDetection.isPad {
                ipad()
            } else {
                iphone()
            }
        }
    }
    
    /// 根据屏幕尺寸应用不同的修饰符
    func adaptForScreenSize<Content: View>(
        compact: @escaping () -> Content,
        regular: @escaping () -> Content,
        large: @escaping () -> Content
    ) -> some View {
        Group {
            switch DeviceDetection.screenSize {
            case .compact, .ipadMini:
                compact()
            case .regular, .ipadRegular, .ipadAir:
                regular()
            case .large, .ipadPro11, .ipadPro12:
                large()
            }
        }
    }
    
    /// 仅在iPad上应用修饰符
    func iPadOnly<Content: View>(@ViewBuilder content: @escaping () -> Content) -> some View {
        Group {
            if DeviceDetection.isPad {
                content()
            } else {
                self
            }
        }
    }
    
    /// 仅在iPhone上应用修饰符
    func iPhoneOnly<Content: View>(@ViewBuilder content: @escaping () -> Content) -> some View {
        Group {
            if DeviceDetection.isPhone {
                content()
            } else {
                self
            }
        }
    }
}
