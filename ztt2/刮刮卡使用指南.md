# 刮刮卡功能使用指南

## 概述
我是Claude Sonnet 4模型。刮刮卡功能已完全集成到ztt2项目中，为用户提供有趣的积分消费体验。

## 功能特点

### 🎯 核心功能
- **真实刮除体验** - 用手指刮开涂层，发现惊喜奖品
- **精美动画效果** - 浮动、3D旋转、粒子效果、庆祝动画
- **完整数据集成** - 积分扣除、记录保存、配置管理
- **响应式设计** - 适配不同设备和屏幕尺寸
- **触觉反馈** - 增强用户体验

### 🎨 视觉设计
- **渐变背景** - 红色到绿色的美观渐变
- **卡片动画** - 浮动效果让卡片更生动
- **粒子特效** - 刮除时的粒子飞溅效果
- **庆祝动画** - 中奖时的华丽庆祝效果

## 使用流程

### 1. 进入刮刮卡
1. 打开成员详情页
2. 点击"抽奖"按钮
3. 在弹出的选项中选择"刮刮卡"
4. 进入刮刮卡主界面

### 2. 查看信息
在刮刮卡主界面可以看到：
- **剩余卡片数量** - 还有多少张可以刮
- **每张消耗积分** - 刮一张需要多少积分
- **当前积分状态** - 是否有足够积分

### 3. 刮除卡片
1. 点击选择要刮的卡片
2. 系统会立即扣除积分（防止退回）
3. 进入刮除界面
4. 用手指在卡片上刮除涂层
5. 刮除40%后自动显示奖品

### 4. 领取奖品
1. 查看中奖结果
2. 点击"确认领取"按钮
3. 系统自动保存中奖记录
4. 返回刮刮卡主界面

## 配置要求

### 前置条件
1. **成员积分充足** - 确保成员有足够积分进行刮除
2. **刮刮卡配置** - 需要预先配置刮刮卡奖品和数量

### 配置刮刮卡
目前需要通过代码配置，未来可以通过设置界面配置：

```swift
// 示例配置
let prizes = ["小贴纸", "铅笔", "橡皮", "尺子", "谢谢参与", "小玩具"]
dataManager.saveScratchCardConfig(
    for: member,
    cardCount: 6,
    costPerPlay: 10,
    cardPrizes: prizes
)
```

## 测试功能

### 使用测试视图
项目中包含了 `ScratchCardTestView` 用于测试：

1. 自动创建测试成员
2. 自动配置刮刮卡奖品
3. 提供完整的测试流程

### 测试步骤
1. 运行项目
2. 导航到 `ScratchCardTestView`
3. 点击"创建测试成员"
4. 点击"打开刮刮卡"
5. 体验完整功能

## 技术实现

### 核心组件
- **ScratchCardView** - 主视图
- **ScratchCardCanvasView** - 刮除画布
- **ScratchCardGridView** - 卡片网格
- **ScratchCardResultView** - 结果展示
- **ScratchCardViewModel** - 业务逻辑

### 数据模型
- **ScratchCardItem** - 刮刮卡数据模型
- **ParticleItem** - 粒子效果模型
- **LotteryConfig** - 抽奖配置
- **LotteryRecord** - 抽奖记录

### 动画系统
- **浮动动画** - 卡片自然浮动效果
- **刮除动画** - 实时刮除进度显示
- **粒子系统** - 刮除时的粒子特效
- **庆祝动画** - 中奖时的庆祝效果

## 注意事项

### 积分管理
- 积分在选择卡片时立即扣除
- 防止用户通过退回来避免积分消耗
- 确保数据一致性

### 性能优化
- 粒子数量限制在50个以内
- 动画使用硬件加速
- 内存使用优化

### 用户体验
- 提供触觉反馈
- 清晰的状态指示
- 流畅的动画过渡
- 直观的操作引导

## 故障排除

### 常见问题
1. **积分不足** - 确保成员有足够积分
2. **无配置** - 检查是否已配置刮刮卡奖品
3. **动画卡顿** - 检查设备性能和内存使用

### 调试方法
1. 使用测试视图验证功能
2. 检查控制台日志
3. 验证数据库记录

## 扩展建议

### 功能扩展
1. **更多皮肤** - 添加更多刮刮卡皮肤类型
2. **音效支持** - 添加刮除和中奖音效
3. **统计功能** - 添加使用统计和分析
4. **社交分享** - 支持中奖结果分享

### 界面优化
1. **配置界面** - 可视化的配置管理
2. **历史记录** - 查看历史中奖记录
3. **排行榜** - 成员中奖排行榜

## 总结

刮刮卡功能为ztt2项目增加了有趣的互动元素，通过精美的视觉设计和流畅的动画效果，为用户提供了愉快的积分消费体验。功能完整、性能优良、易于使用和扩展。

🎉 现在就开始体验刮刮卡的乐趣吧！
